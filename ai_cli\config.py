"""
Configuration management for AI CLI
"""

import os
from dataclasses import dataclass
from typing import Optional


@dataclass
class Config:
    """Configuration class for AI CLI application"""

    api_key: Optional[str] = None
    model: str = "openai/gpt-3.5-turbo"
    max_tokens: int = 2048
    temperature: float = 0.7
    base_url: str = "https://openrouter.ai/api/v1"

    def __post_init__(self):
        """Initialize configuration after object creation"""
        # Use provided API key or get from environment
        if not self.api_key:
            self.api_key = os.getenv("OPENROUTER_API_KEY", "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")

    @property
    def headers(self) -> dict:
        """Get headers for API requests"""
        return {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://github.com/ai-cli/ai-cli",
            "X-Title": "AI CLI"
        }