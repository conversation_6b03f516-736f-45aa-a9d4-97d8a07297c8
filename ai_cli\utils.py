"""
Utility functions for AI CLI
"""

import re
from typing import Optional
from rich.console import Console
from rich.syntax import Syntax
from rich.panel import Panel


def detect_code_language(code: str) -> Optional[str]:
    """
    Detect programming language from code snippet

    Args:
        code: Code snippet to analyze

    Returns:
        Detected language or None
    """
    # Simple language detection based on common patterns
    patterns = {
        'python': [
            r'def\s+\w+\s*\(',
            r'import\s+\w+',
            r'from\s+\w+\s+import',
            r'if\s+__name__\s*==\s*["\']__main__["\']',
            r'print\s*\(',
        ],
        'javascript': [
            r'function\s+\w+\s*\(',
            r'const\s+\w+\s*=',
            r'let\s+\w+\s*=',
            r'var\s+\w+\s*=',
            r'console\.log\s*\(',
            r'=>',
        ],
        'java': [
            r'public\s+class\s+\w+',
            r'public\s+static\s+void\s+main',
            r'System\.out\.println',
            r'import\s+java\.',
        ],
        'cpp': [
            r'#include\s*<\w+>',
            r'int\s+main\s*\(',
            r'std::',
            r'cout\s*<<',
            r'cin\s*>>',
        ],
        'c': [
            r'#include\s*<\w+\.h>',
            r'int\s+main\s*\(',
            r'printf\s*\(',
            r'scanf\s*\(',
        ],
        'bash': [
            r'#!/bin/bash',
            r'#!/bin/sh',
            r'\$\w+',
            r'echo\s+',
        ],
        'sql': [
            r'SELECT\s+',
            r'FROM\s+\w+',
            r'WHERE\s+',
            r'INSERT\s+INTO',
            r'UPDATE\s+\w+\s+SET',
        ],
        'html': [
            r'<html>',
            r'<head>',
            r'<body>',
            r'<div\s*.*?>',
        ],
        'css': [
            r'\w+\s*\{[^}]*\}',
            r'@media\s+',
            r'#\w+\s*\{',
            r'\.\w+\s*\{',
        ],
        'json': [
            r'^\s*\{',
            r'^\s*\[',
            r'"\w+"\s*:',
        ],
        'yaml': [
            r'^\w+\s*:',
            r'^\s*-\s+\w+',
        ],
    }

    # Count matches for each language
    scores = {}
    for lang, lang_patterns in patterns.items():
        score = 0
        for pattern in lang_patterns:
            if re.search(pattern, code, re.MULTILINE | re.IGNORECASE):
                score += 1
        if score > 0:
            scores[lang] = score

    # Return language with highest score
    if scores:
        return max(scores, key=scores.get)

    return None


def format_code_block(code: str, language: Optional[str] = None) -> Panel:
    """
    Format code block with syntax highlighting

    Args:
        code: Code to format
        language: Programming language (auto-detected if None)

    Returns:
        Rich Panel with formatted code
    """
    if language is None:
        language = detect_code_language(code)

    if language:
        syntax = Syntax(code, language, theme="monokai", line_numbers=True)
        return Panel(syntax, title=f"Code ({language})", border_style="green")
    else:
        return Panel(code, title="Code", border_style="yellow")


def extract_code_blocks(text: str) -> list:
    """
    Extract code blocks from markdown-style text

    Args:
        text: Text containing potential code blocks

    Returns:
        List of tuples (code, language)
    """
    # Pattern for fenced code blocks
    pattern = r'```(\w+)?\n(.*?)\n```'
    matches = re.findall(pattern, text, re.DOTALL)

    code_blocks = []
    for lang, code in matches:
        code_blocks.append((code.strip(), lang if lang else None))

    return code_blocks


def is_multiline_input(text: str) -> bool:
    """
    Check if input appears to be multiline code or text

    Args:
        text: Input text to check

    Returns:
        True if appears to be multiline input
    """
    # Check for common multiline indicators
    multiline_indicators = [
        r'def\s+\w+\s*\(',  # Python function
        r'class\s+\w+',     # Class definition
        r'if\s+.*:$',       # If statement
        r'for\s+.*:$',      # For loop
        r'while\s+.*:$',    # While loop
        r'try\s*:$',        # Try block
        r'with\s+.*:$',     # With statement
        r'\{$',             # Opening brace
        r'function\s+\w+',  # JavaScript function
    ]

    for pattern in multiline_indicators:
        if re.search(pattern, text, re.MULTILINE):
            return True

    return False


def truncate_text(text: str, max_length: int = 100) -> str:
    """
    Truncate text to specified length with ellipsis

    Args:
        text: Text to truncate
        max_length: Maximum length

    Returns:
        Truncated text
    """
    if len(text) <= max_length:
        return text
    return text[:max_length-3] + "..."