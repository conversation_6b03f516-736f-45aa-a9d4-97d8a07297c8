Metadata-Version: 2.4
Name: ai-cli
Version: 1.0.0
Summary: AI-powered CLI terminal application using OpenRouter API
Author: AI CLI Developer
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Requires-Python: >=3.8
Description-Content-Type: text/markdown
Requires-Dist: click>=8.0.0
Requires-Dist: requests>=2.28.0
Requires-Dist: rich>=13.0.0
Requires-Dist: pygments>=2.14.0
Requires-Dist: prompt-toolkit>=3.0.0
Dynamic: author
Dynamic: classifier
Dynamic: description
Dynamic: description-content-type
Dynamic: requires-dist
Dynamic: requires-python
Dynamic: summary

# AI CLI - AI-Powered Command Line Interface

🤖 A powerful command-line interface that allows you to interact with AI assistants directly from your terminal using OpenRouter's API.

## Features

- **Interactive Chat Interface**: Engage in conversations with AI assistants similar to Claude
- **Code Syntax Highlighting**: Automatic detection and highlighting of code blocks in multiple programming languages
- **Multi-line Input Support**: Handle complex queries and code snippets with ease
- **Command History**: Navigate through previous commands using arrow keys
- **Auto-completion**: Built-in command completion for faster interaction
- **Rich Formatting**: Beautiful terminal output with markdown rendering
- **OpenRouter Integration**: Access to multiple AI models through OpenRouter's API
- **Error Handling**: Robust error handling with clear error messages

## Installation

### Prerequisites

- Python 3.8 or higher
- pip (Python package installer)

### Install from Source

1. Clone or download this repository:
```bash
git clone <repository-url>
cd ai-cli
```

2. Install the package:
```bash
pip install -e .
```

Or install dependencies manually:
```bash
pip install -r requirements.txt
```

## Configuration

### API Key Setup

You can provide your OpenRouter API key in two ways:

1. **Command Line Option** (recommended for testing):
```bash
ai-cli chat --api-key YOUR_API_KEY
```

2. **Environment Variable** (recommended for regular use):
```bash
export OPENROUTER_API_KEY="YOUR_API_KEY"
ai-cli chat
```

### Default API Key

For convenience, this application includes a default OpenRouter API key:
```
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
```

**Note**: This is a shared key for demonstration purposes. For production use, please obtain your own API key from [OpenRouter](https://openrouter.ai/).

## Usage

### Basic Commands

Start an interactive chat session:
```bash
ai-cli chat
```

Start chat with specific model:
```bash
ai-cli chat --model "anthropic/claude-3-sonnet"
```

Start chat with custom settings:
```bash
ai-cli chat --api-key YOUR_KEY --model "openai/gpt-4" --max-tokens 4096
```

Show application information:
```bash
ai-cli info
```

### Interactive Commands

Once in a chat session, you can use these commands:

- `/help` - Show available commands
- `/clear` - Clear conversation history
- `/history` - Show conversation history
- `/model` - Display current AI model
- `/exit` or `/quit` - Exit the chat session

### Example Session

```
$ ai-cli chat
🚀 Starting AI Chat Session...
✓ API connection successful!
Model: openai/gpt-3.5-turbo
Max Tokens: 2048

Type '/help' for commands or start chatting!
Press Ctrl+C to exit

You: Hello! Can you help me write a Python function to calculate fibonacci numbers?

AI: I'd be happy to help you write a Python function to calculate Fibonacci numbers! Here are a few different approaches:

## Recursive Approach (Simple but inefficient for large numbers)

```python
def fibonacci_recursive(n):
    """Calculate the nth Fibonacci number using recursion."""
    if n <= 1:
        return n
    return fibonacci_recursive(n - 1) + fibonacci_recursive(n - 2)
```

## Iterative Approach (Efficient)

```python
def fibonacci_iterative(n):
    """Calculate the nth Fibonacci number using iteration."""
    if n <= 1:
        return n

    a, b = 0, 1
    for _ in range(2, n + 1):
        a, b = b, a + b
    return b
```

The iterative approach is much more efficient for larger numbers!

You: /exit
Goodbye!
```

## Supported AI Models

The application supports various AI models available through OpenRouter:

- `openai/gpt-3.5-turbo` (default)
- `openai/gpt-4`
- `anthropic/claude-3-sonnet`
- `anthropic/claude-3-haiku`
- `meta-llama/llama-2-70b-chat`
- And many more available through OpenRouter

## Development

### Project Structure

```
ai-cli/
├── ai_cli/
│   ├── __init__.py          # Package initialization
│   ├── main.py              # CLI entry point
│   ├── config.py            # Configuration management
│   ├── api_client.py        # OpenRouter API client
│   ├── chat.py              # Interactive chat interface
│   └── utils.py             # Utility functions
├── requirements.txt         # Python dependencies
├── setup.py                # Package setup
└── README.md               # This file
```

### Dependencies

- `click` - Command-line interface framework
- `requests` - HTTP library for API calls
- `rich` - Rich text and beautiful formatting
- `pygments` - Syntax highlighting
- `prompt-toolkit` - Interactive command line tools

## Troubleshooting

### Common Issues

1. **API Connection Failed**
   - Check your internet connection
   - Verify your API key is correct
   - Ensure OpenRouter service is available

2. **Import Errors**
   - Make sure all dependencies are installed: `pip install -r requirements.txt`
   - Check Python version (3.8+ required)

3. **Command Not Found**
   - If installed with `-e .`, make sure the package is properly installed
   - Try running directly: `python -m ai_cli.main chat`

### Getting Help

If you encounter issues:

1. Check the error message for specific details
2. Verify your API key and internet connection
3. Try running with verbose output
4. Check the OpenRouter API status

## License

This project is licensed under the MIT License.

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## Acknowledgments

- [OpenRouter](https://openrouter.ai/) for providing access to multiple AI models
- [Rich](https://github.com/Textualize/rich) for beautiful terminal formatting
- [Click](https://click.palletsprojects.com/) for the CLI framework
