#!/usr/bin/env python3
"""
Main CLI application entry point
"""

import click
import sys
import os
from rich.console import Console
from rich.panel import Panel
from rich.text import Text

from .chat import ChatInterface
from .config import Config

console = Console()

@click.group()
@click.version_option(version="1.0.0")
def cli():
    """AI CLI - An AI-powered command-line interface using OpenRouter API"""
    pass

@cli.command()
@click.option('--api-key', help='OpenRouter API key (can also be set via OPENROUTER_API_KEY env var)')
@click.option('--model', default='openai/gpt-3.5-turbo', help='AI model to use')
@click.option('--max-tokens', default=2048, help='Maximum tokens for responses')
def chat(api_key, model, max_tokens):
    """Start an interactive chat session with the AI assistant"""

    # Display welcome message
    welcome_text = Text("🤖 AI CLI Assistant", style="bold cyan")
    welcome_panel = Panel(
        welcome_text,
        title="Welcome",
        border_style="cyan",
        padding=(1, 2)
    )
    console.print(welcome_panel)
    console.print()

    # Initialize configuration
    config = Config(api_key=api_key, model=model, max_tokens=max_tokens)

    if not config.api_key:
        console.print("[red]Error: No API key provided. Use --api-key or set OPENROUTER_API_KEY environment variable.[/red]")
        sys.exit(1)

    # Start chat interface
    chat_interface = ChatInterface(config)
    chat_interface.start()

@cli.command()
def info():
    """Display information about the AI CLI application"""
    info_text = """
    AI CLI - AI-powered Command Line Interface

    Features:
    • Interactive chat with AI assistant
    • Code syntax highlighting
    • Multi-line input support
    • Command history
    • OpenRouter API integration

    Usage:
    ai-cli chat --api-key YOUR_API_KEY

    Environment Variables:
    OPENROUTER_API_KEY - Your OpenRouter API key
    """

    console.print(Panel(info_text, title="AI CLI Information", border_style="blue"))

def main():
    """Main entry point for the CLI application"""
    try:
        cli()
    except KeyboardInterrupt:
        console.print("\n[yellow]Goodbye![/yellow]")
        sys.exit(0)
    except Exception as e:
        console.print(f"[red]Error: {e}[/red]")
        sys.exit(1)

if __name__ == "__main__":
    main()